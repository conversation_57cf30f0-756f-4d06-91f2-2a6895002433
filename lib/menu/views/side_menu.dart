import 'package:flutter/material.dart';

import '../../components/icon/zuz_icons.dart';
import '../../components/icon/zuz_logo.dart';
import '../../theme/colors.dart';

class SideMenu extends StatelessWidget {
  const SideMenu({
    super.key,
    required this.operationsTitle,
    required this.assessmentsLabel,
    required this.onAssessmentsTap,
    required this.herdLabel,
    required this.onHerdTap,
    required this.propertiesLabel,
    required this.onPropertiesTap,
    required this.packagesLabel,
    required this.onPackagesTap,
    required this.configurationTitle,
    required this.teamLabel,
    required this.onTeamTap,
    required this.accountLabel,
    required this.onAccountTap,
    required this.logoutLabel,
    required this.onLogoutTap,
    required this.onClose,
  });

  final String operationsTitle;
  final String assessmentsLabel;
  final VoidCallback onAssessmentsTap;
  final String herdLabel;
  final VoidCallback onHerdTap;
  final String propertiesLabel;
  final VoidCallback onPropertiesTap;
  final String packagesLabel;
  final VoidCallback onPackagesTap;
  final String configurationTitle;
  final String teamLabel;
  final VoidCallback onTeamTap;
  final String accountLabel;
  final VoidCallback onAccountTap;
  final String logoutLabel;
  final VoidCallback onLogoutTap;
  final VoidCallback onClose;

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: SafeArea(
        child: Column(
          children: [
            // Cabeçalho
            // DrawerHeader(
            //   child: Row(
            //     children: [
            //       // Logo
            //       ZuzLogo.small(),

            //       // Botão para fechar
            //       IconButton(
            //         icon: const Icon(ZuzIcons.close),
            //         onPressed: onClose,
            //       ),
            //     ],
            //   ),
            // ),
            Row(
              children: [
                // Logo
                ZuzLogo.small(),

                // Botão para fechar
                IconButton(
                  icon: const Icon(ZuzIcons.close),
                  onPressed: onClose,
                ),
              ],
            ),

            // Menu
            Flexible(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Operação ZUZ
                    // Text(operationsTitle),
                    _MenuTitle(text: Text(operationsTitle)),
                    ListTile(
                      title: Text(assessmentsLabel),
                      onTap: onAssessmentsTap,
                    ),
                    ListTile(title: Text(herdLabel), onTap: onHerdTap),
                    ListTile(
                      title: Text(propertiesLabel),
                      onTap: onPropertiesTap,
                    ),
                    ListTile(title: Text(packagesLabel), onTap: onPackagesTap),

                    // Configuração
                    Divider(height: 20),
                    Text(configurationTitle),
                    ListTile(title: Text(teamLabel), onTap: onTeamTap),
                    ListTile(title: Text(accountLabel), onTap: onAccountTap),

                    // Sair
                    Spacer(),
                    ListTile(
                      leading: Icon(ZuzIcons.logout, color: AppColors.error),
                      title: Text(logoutLabel),
                      onTap: onLogoutTap,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _MenuTitle extends ListTile {
  _MenuTitle({super.key, required BuildContext context, required String text})
    : super(
        title: Text(text),
        titleTextStyle: TextTheme.of(
          context,
        ).bodySmall?.copyWith(color: AppColors.gray5),
      );
}

class _MenuItem extends ListTile {
  _MenuItem({
    super.key,
    required BuildContext context,
    required String text,
    VoidCallback? onTap,
  }) : super(
         title: Text(text),
         onTap: onTap,
         titleTextStyle: TextTheme.of(context).bodyMedium,
       );
}
